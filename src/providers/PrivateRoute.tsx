/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from "react";
import { Navigate, Outlet } from "react-router-dom";
import { useRecoilValue } from "recoil";
import { auth } from "@/atoms";

// const isLogin = !!sessionStorage.auth;
// console.log(isLogin);

const PrivateRoute = () => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(true);
  const accessToken = useRecoilValue<string>(auth);
  useEffect(() => {
    const token: string = accessToken;
    setIsAuthenticated(!!token);
    // console.log(isAuthenticated);
  }, []);
  // return <Outlet />
  return isAuthenticated ? <Outlet /> : <Navigate to="/login" />;
};

export default PrivateRoute;
