import React, { lazy, Suspense } from "react";
import { createBrowser<PERSON>outer, RouterProvider } from "react-router-dom";
import PrivateRoute from "./PrivateRoute";

// Lazy imports for code splitting
const Dashboard = lazy(() => import("../pages/dashboard"));
const Root = lazy(() => import("../pages/root"));
const NotFoundPage = lazy(() => import("../pages/not_found"));
const LoginPage = lazy(() => import("../pages/login"));
const AppUsersPage = lazy(() => import("../pages/app_users"));
const AnalysisFailurePage = lazy(() =>
  import("../pages/analysis").then((module) => ({
    default: module.AnalysisFailurePage,
  }))
);
const AnalysisSuccessPage = lazy(() =>
  import("../pages/analysis").then((module) => ({
    default: module.AnalysisSuccessPage,
  }))
);
const StatisticPage = lazy(() =>
  import("../pages/statistic").then((module) => ({
    default: module.StatisticPage,
  }))
);
const BackUsersPage = lazy(() =>
  import("../pages/back_users").then((module) => ({
    default: module.BackUsersPage,
  }))
);

const router = createBrowserRouter([
  {
    path: "/login",
    element: (
      <Suspense fallback={<div>Loading...</div>}>
        <LoginPage />
      </Suspense>
    ),
  },
  {
    path: "/",
    element: <PrivateRoute />,
    children: [
      {
        path: "/",
        element: (
          <Suspense fallback={<div>Loading...</div>}>
            <Root />
          </Suspense>
        ),
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<div>Loading...</div>}>
                <Dashboard />
              </Suspense>
            ),
          },
          {
            path: "app_users",
            element: (
              <Suspense fallback={<div>Loading...</div>}>
                <AppUsersPage />
              </Suspense>
            ),
          },
          {
            path: "analysis/success",
            element: (
              <Suspense fallback={<div>Loading...</div>}>
                <AnalysisSuccessPage />
              </Suspense>
            ),
          },
          {
            path: "analysis/failure",
            element: (
              <Suspense fallback={<div>Loading...</div>}>
                <AnalysisFailurePage />
              </Suspense>
            ),
          },
          {
            path: "statistic",
            element: (
              <Suspense fallback={<div>Loading...</div>}>
                <StatisticPage />
              </Suspense>
            ),
          },
          {
            path: "back_users",
            element: (
              <Suspense fallback={<div>Loading...</div>}>
                <BackUsersPage />
              </Suspense>
            ),
          },
          {
            path: "*",
            element: (
              <Suspense fallback={<div>Loading...</div>}>
                <NotFoundPage />
              </Suspense>
            ),
          },
        ],
      },
    ],
  },
]);

export const Router = () => {
  return <RouterProvider router={router} />;
};
