/* eslint-disable react-hooks/exhaustive-deps */
import { Router } from "@/providers/Router";
import { Global } from "./styles";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { Suspense, useEffect, useState } from "react";
import {
  QueryCache,
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { RecoilRoot } from "recoil";
import { useDarkMode, useApiErrors, useNetworkOffline } from "@/hooks";
import { DarkModeProvider } from "./contexts/DarModeContext";

const THEME = createTheme({
  typography: {
    fontFamily: `"Gilroy", "Helvetica", "Arial", sans-serif`,
  },
  palette: {
    text: {
      primary: "var(--color-text-dark)",
    },
    primary: {
      main: "#41D8E6",
    },
  },
  components: {
    MuiSvgIcon: {
      styleOverrides: {
        root: {
          fill: "var(--color-underline-dark)",
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          "&:hover": {
            backgroundColor: "rgba(218, 218, 218, 0.40) !important",
          },
        },
      },
    },
  },
});

const App = () => {
  const isNetworkOffline: boolean = useNetworkOffline();
  const { updateDarkMode, handleDarkModeChange } = useDarkMode();
  const { handleError } = useApiErrors();
  const [queryClient] = useState(
    new QueryClient({
      defaultOptions: {
        queries: {
          refetchOnWindowFocus: false,
          retry: false,
          staleTime: 1000,
        },
      },
      queryCache: new QueryCache({
        onSuccess: (data) => {
          console.log(data);
        },
        onError: handleError,
      }),
    })
  );

  useEffect(() => {
    if (isNetworkOffline) {
      alert("네트워크 연결이 불안정합니다.");
      return;
    }

    const isDark: boolean =
      localStorage.theme === "dark" ||
      (!("theme" in localStorage) &&
        window.matchMedia("(prefers-color-scheme: dark)").matches);

    updateDarkMode(isDark);
    handleDarkModeChange(isDark);
  }, []);

  return (
    <ThemeProvider theme={THEME}>
      <Suspense fallback={<>Loading...</>}>
        <QueryClientProvider client={queryClient}>
          <RecoilRoot>
            <Global />
            <DarkModeProvider>
              <Router />
            </DarkModeProvider>
          </RecoilRoot>
          <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
      </Suspense>
    </ThemeProvider>
  );
};

export default App;
