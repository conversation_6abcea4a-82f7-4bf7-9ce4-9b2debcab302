import { Browser<PERSON>outer, Route, Routes } from "react-router-dom";
import { lazy, Suspense } from "react";
import PrivateRoute from "./PrivateRoute";

// Lazy imports for code splitting
const Dashboard = lazy(() => import("./pages/dashboard"));
const Root = lazy(() => import("./pages/root"));
const NotFoundPage = lazy(() => import("./pages/not_found"));
const LoginPage = lazy(() => import("./pages/login"));
const AppUsersPage = lazy(() => import("./pages/app_users"));
const AnalysisFailurePage = lazy(() =>
  import("./pages/analysis").then((module) => ({
    default: module.AnalysisFailurePage,
  }))
);
const AnalysisSuccessPage = lazy(() =>
  import("./pages/analysis").then((module) => ({
    default: module.AnalysisSuccessPage,
  }))
);
const StatisticPage = lazy(() =>
  import("./pages/statistic").then((module) => ({
    default: module.StatisticPage,
  }))
);
const BackUsersPage = lazy(() =>
  import("./pages/back_users").then((module) => ({
    default: module.BackUsersPage,
  }))
);

export const Router = () => {
  return (
    <BrowserRouter>
      <Suspense fallback={<div>Loading...</div>}>
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route element={<PrivateRoute />}>
            <Route path="/" element={<Root />}>
              <Route path="/" element={<Dashboard />} />
              <Route path="/app_users" element={<AppUsersPage />} />
              <Route path="/analysis">
                <Route path="success" element={<AnalysisSuccessPage />} />
                <Route path="failure" element={<AnalysisFailurePage />} />
              </Route>
              <Route path="/statistic" element={<StatisticPage />} />
              <Route path="/back_users" element={<BackUsersPage />} />
              <Route path="/*" element={<NotFoundPage />} />
            </Route>
          </Route>
        </Routes>
      </Suspense>
    </BrowserRouter>
  );
};
