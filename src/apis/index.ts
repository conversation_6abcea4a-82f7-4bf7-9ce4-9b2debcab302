// apis/axiosInstance.ts
import axios, { AxiosInstance } from "axios";
import { setInterceptors } from "./common/interceptor";

const BASE_URL = import.meta.env.VITE_APP_API_URL || "";

console.log(import.meta.env.VITE_APP_API_URL)

const createInstance = (withAuth: boolean = false): AxiosInstance => {
  const instance = axios.create({
    baseURL: BASE_URL,
    // timeout: 5000,
    withCredentials: true,
  });

  return withAuth ? setInterceptors(instance) : instance;
};

export const instance = createInstance();
export const AuthInstance = createInstance(true);
