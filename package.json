{"name": "backoffice-pet", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "local": "vite --mode local", "build": "tsc && vite build", "build:dev": "env-cmd -f .env.development tsc && vite build", "build:prod": "env-cmd -f .env.production tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "deploy-dev": "aws s3 sync ./dist s3://backoffice-pet-dev --profile=cym-front", "invalidate-dev": "aws cloudfront create-invalidation --profile=cym-front --distribution-id EO7Y4UWO0KFH --paths '/*'", "deploy": "aws s3 sync ./dist s3://backoffice-pet --profile=cym-front", "invalidate": "aws cloudfront create-invalidation --profile=cym-front --distribution-id E2MTMIAV0B3A1K --paths '/*'"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fortawesome/fontawesome-free": "^6.5.1", "@mui/material": "^5.15.0", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@tanstack/react-query": "^5.17.12", "@tanstack/react-query-devtools": "^5.17.12", "@types/axios": "^0.14.0", "axios": "^1.6.5", "base-64": "^1.0.0", "chart.js": "^4.4.1", "chartjs-plugin-datalabels": "^2.2.0", "env-cmd": "^10.1.0", "react": "^18.2.0", "react-calendar": "^4.8.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-image-magnify": "^2.7.4", "react-router-dom": "^6.20.1", "recoil": "^0.7.7"}, "devDependencies": {"@types/base-64": "^1.0.2", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-image-magnify": "^2", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8"}, "packageManager": "yarn@4.9.1"}